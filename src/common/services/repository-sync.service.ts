import { DatabaseService } from '@/common/services/database.service';
import { GiteaRepositoryResponseDto, GiteaService } from '@/common/services/gitea.service';
import { giteaProfiles, type GiteaProfile } from '@/database/schema/gitea-profiles.schema';
import { giteaRepositories, type GiteaRepository as DbGiteaRepository, type NewGiteaRepository } from '@/database/schema/gitea-repositories.schema';
import { MarketplaceMetadataDto } from '@/developer/dto/marketplace-metadata.dto';
import { RepositorySyncResponseDto } from '@/developer/dto/repository-sync-response.dto';
import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';

@Injectable()
export class RepositorySyncService {
  private readonly logger = new Logger(RepositorySyncService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly giteaService: GiteaService,
  ) {}

  /**
   * Sync all repositories for a developer
   */
  async syncDeveloperRepositories(userId: string): Promise<RepositorySyncResponseDto> {
    const profile = await this.getDeveloperProfile(userId);

    if (!profile.isProvisioned) {
      throw new BadRequestException('Developer profile not provisioned with Gitea');
    }

    try {
      // Get repositories from Gitea
      const giteaRepos = await this.giteaService.getUserRepositories(profile.giteaUsername);

      const result: RepositorySyncResponseDto = {
        synced: 0,
        created: 0,
        updated: 0,
        errors: [],
      };

      // Process each repository
      for (const giteaRepo of giteaRepos) {
        try {
          const syncResult = await this.syncRepository(profile.id, giteaRepo);
          if (syncResult.created) {
            result.created++;
          } else {
            result.updated++;
          }
          result.synced++;
        } catch (error) {
          result.errors.push(`${giteaRepo.full_name}: ${error.message}`);
          this.logger.error(`Failed to sync repository ${giteaRepo.full_name}:`, error.message);
        }
      }

      // Update profile statistics
      await this.updateProfileStats(profile.id);

      this.logger.log(`Synced repositories for developer ${profile.giteaUsername}: ${result.synced} total, ${result.created} created, ${result.updated} updated, ${result.errors.length} errors`);

      return result;
    } catch (error) {
      this.logger.error(`Failed to sync repositories for user ${userId}:`, error.message);
      throw new BadRequestException(`Failed to sync repositories: ${error.message}`);
    }
  }

  /**
   * Sync a single repository
   */
  async syncRepository(giteaProfileId: string, giteaRepo: GiteaRepositoryResponseDto): Promise<{ created: boolean }> {
    // Check if repository already exists
    const existingRepo = await this.db.db
      .select()
      .from(giteaRepositories)
      .where(
        and(
          eq(giteaRepositories.giteaProfileId, giteaProfileId),
          eq(giteaRepositories.giteaRepoId, giteaRepo.id)
        )
      )
      .limit(1);

    const marketplaceMetadata = await this.extractMarketplaceMetadata(giteaRepo);

    if (existingRepo.length === 0) {
      // Create new repository record
      const newRepo: NewGiteaRepository = {
        giteaProfileId,
        giteaRepoId: giteaRepo.id,
        name: giteaRepo.name,
        fullName: giteaRepo.full_name,
        description: giteaRepo.description || null,
        visibility: giteaRepo.private ? 'private' : 'public',
        isFork: giteaRepo.fork,
        isTemplate: giteaRepo.template,
        isArchived: giteaRepo.archived,
        isEmpty: giteaRepo.empty,
        size: giteaRepo.size,
        starsCount: giteaRepo.stars_count,
        forksCount: giteaRepo.forks_count,
        watchersCount: giteaRepo.watchers_count,
        openIssuesCount: giteaRepo.open_issues_count,
        defaultBranch: giteaRepo.default_branch,
        language: giteaRepo.language || null,
        topics: [], // Gitea doesn't provide topics in basic repo info
        htmlUrl: giteaRepo.html_url,
        cloneUrl: giteaRepo.clone_url,
        sshUrl: giteaRepo.ssh_url,
        hasMarketplaceMetadata: !!marketplaceMetadata,
        marketplaceMetadata,
        syncStatus: 'completed',
        lastSyncAt: new Date(),
        giteaCreatedAt: new Date(giteaRepo.created_at),
        giteaUpdatedAt: new Date(giteaRepo.updated_at),
        giteaPushedAt: giteaRepo.pushed_at ? new Date(giteaRepo.pushed_at) : null,
      };

      await this.db.db
        .insert(giteaRepositories)
        .values(newRepo);

      this.logger.log(`Created repository record: ${giteaRepo.full_name}`);
      return { created: true };
    } else {
      // Update existing repository record
      await this.db.db
        .update(giteaRepositories)
        .set({
          name: giteaRepo.name,
          fullName: giteaRepo.full_name,
          description: giteaRepo.description || null,
          visibility: giteaRepo.private ? 'private' : 'public',
          isFork: giteaRepo.fork,
          isTemplate: giteaRepo.template,
          isArchived: giteaRepo.archived,
          isEmpty: giteaRepo.empty,
          size: giteaRepo.size,
          starsCount: giteaRepo.stars_count,
          forksCount: giteaRepo.forks_count,
          watchersCount: giteaRepo.watchers_count,
          openIssuesCount: giteaRepo.open_issues_count,
          defaultBranch: giteaRepo.default_branch,
          language: giteaRepo.language || null,
          htmlUrl: giteaRepo.html_url,
          cloneUrl: giteaRepo.clone_url,
          sshUrl: giteaRepo.ssh_url,
          hasMarketplaceMetadata: !!marketplaceMetadata,
          marketplaceMetadata,
          syncStatus: 'completed',
          lastSyncAt: new Date(),
          giteaUpdatedAt: new Date(giteaRepo.updated_at),
          giteaPushedAt: giteaRepo.pushed_at ? new Date(giteaRepo.pushed_at) : null,
          updatedAt: new Date(),
        })
        .where(eq(giteaRepositories.id, existingRepo[0].id));

      this.logger.log(`Updated repository record: ${giteaRepo.full_name}`);
      return { created: false };
    }
  }

  /**
   * Get repositories for a developer
   */
  async getDeveloperRepositories(userId: string, includePrivate: boolean = true): Promise<DbGiteaRepository[]> {
    const profile = await this.getDeveloperProfile(userId);

    if (includePrivate) {
      return await this.db.db
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.giteaProfileId, profile.id));
    } else {
      return await this.db.db
        .select()
        .from(giteaRepositories)
        .where(
          and(
            eq(giteaRepositories.giteaProfileId, profile.id),
            eq(giteaRepositories.visibility, 'public')
          )
        );
    }
  }

  /**
   * Get repository by ID
   */
  async getRepository(repositoryId: string): Promise<DbGiteaRepository> {
    const repos = await this.db.db
      .select()
      .from(giteaRepositories)
      .where(eq(giteaRepositories.id, repositoryId))
      .limit(1);

    if (repos.length === 0) {
      throw new NotFoundException('Repository not found');
    }

    return repos[0];
  }

  /**
   * Update repository marketplace metadata
   */
  async updateRepositoryMarketplaceMetadata(
    repositoryId: string,
    metadata: MarketplaceMetadataDto
  ): Promise<DbGiteaRepository> {
    const updatedRepos = await this.db.db
      .update(giteaRepositories)
      .set({
        marketplaceMetadata: metadata,
        hasMarketplaceMetadata: true,
        updatedAt: new Date(),
      })
      .where(eq(giteaRepositories.id, repositoryId))
      .returning();

    if (updatedRepos.length === 0) {
      throw new NotFoundException('Repository not found');
    }

    this.logger.log(`Updated marketplace metadata for repository: ${updatedRepos[0].fullName}`);
    return updatedRepos[0];
  }

  /**
   * Mark repository as published to marketplace
   */
  async markRepositoryAsPublished(repositoryId: string, marketplaceItemId: string): Promise<DbGiteaRepository> {
    const updatedRepos = await this.db.db
      .update(giteaRepositories)
      .set({
        isPublished: true,
        marketplaceItemId,
        updatedAt: new Date(),
      })
      .where(eq(giteaRepositories.id, repositoryId))
      .returning();

    if (updatedRepos.length === 0) {
      throw new NotFoundException('Repository not found');
    }

    // Update profile published count
    const repo = updatedRepos[0];
    await this.updateProfileStats(repo.giteaProfileId);

    this.logger.log(`Marked repository as published: ${repo.fullName}`);
    return repo;
  }

  /**
   * Unmark repository as published from marketplace
   */
  async unmarkRepositoryAsPublished(repositoryId: string): Promise<DbGiteaRepository> {
    const updatedRepos = await this.db.db
      .update(giteaRepositories)
      .set({
        isPublished: false,
        marketplaceItemId: null,
        updatedAt: new Date(),
      })
      .where(eq(giteaRepositories.id, repositoryId))
      .returning();

    if (updatedRepos.length === 0) {
      throw new NotFoundException('Repository not found');
    }

    // Update profile published count
    const repo = updatedRepos[0];
    await this.updateProfileStats(repo.giteaProfileId);

    this.logger.log(`Unmarked repository as published: ${repo.fullName}`);
    return repo;
  }

  // Private helper methods
  private async getDeveloperProfile(userId: string): Promise<GiteaProfile> {
    const profiles = await this.db.db
      .select()
      .from(giteaProfiles)
      .where(eq(giteaProfiles.userId, userId))
      .limit(1);

    if (profiles.length === 0) {
      throw new NotFoundException('Developer profile not found');
    }

    return profiles[0];
  }

  private async updateProfileStats(giteaProfileId: string): Promise<void> {
    // Get repository counts
    const repos = await this.db.db
      .select()
      .from(giteaRepositories)
      .where(eq(giteaRepositories.giteaProfileId, giteaProfileId));

    const totalRepositories = repos.length;
    const publicRepositories = repos.filter(repo => repo.visibility === 'public').length;
    const privateRepositories = repos.filter(repo => repo.visibility === 'private').length;
    const publishedRepositories = repos.filter(repo => repo.isPublished).length;

    // Update profile
    await this.db.db
      .update(giteaProfiles)
      .set({
        totalRepositories,
        publicRepositories,
        privateRepositories,
        publishedRepositories
      })
      .where(eq(giteaProfiles.id, giteaProfileId));
  }

  private async extractMarketplaceMetadata(__giteaRepo: GiteaRepositoryResponseDto): Promise<MarketplaceMetadataDto | null> {
    // TODO: Implement marketplace.yml file parsing from repository
    // For now, return null - this would be implemented to fetch and parse
    // a marketplace.yml or marketplace.json file from the repository

    // This would involve:
    // 1. Fetching the file content from Gitea API
    // 2. Parsing YAML/JSON
    // 3. Validating the structure
    // 4. Returning the parsed metadata

    return null;
  }
}
