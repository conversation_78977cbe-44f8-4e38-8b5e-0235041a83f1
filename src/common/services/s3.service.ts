import {
    C<PERSON><PERSON><PERSON>et<PERSON>ommand,
    DeleteObject<PERSON>ommand,
    Get<PERSON><PERSON><PERSON>ommand,
    HeadObjectCommand,
    PutBucketCorsCommand,
    PutObjectCommand,
    S3Client
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as mimeTypes from 'mime-types';

export interface UploadOptions {
  bucket?: string;
  key: string;
  body: Buffer;
  contentType?: string;
  metadata?: Record<string, string>;
  expires?: number; // TTL in seconds
}

export interface PresignedUrlOptions {
  bucket?: string;
  key: string;
  expires?: number; // TTL in seconds
  contentType?: string;
}

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;
  private readonly externalS3Client: S3Client;
  private readonly defaultBucket: string;
  private readonly internalEndpoint: string;
  private readonly externalEndpoint: string;

  constructor(private configService: ConfigService) {
    const endpoint = this.configService.get<string>('S3_ENDPOINT', 'http://localhost:9000');
    const region = this.configService.get<string>('S3_REGION', 'us-east-1');
    const accessKeyId = this.configService.get<string>('S3_ACCESS_KEY', 'minioadmin');
    const secretAccessKey = this.configService.get<string>('S3_SECRET_KEY', 'minioadmin');
    const forcePathStyle = this.configService.get<boolean>('S3_FORCE_PATH_STYLE', true);

    this.defaultBucket = this.configService.get<string>('S3_BUCKET_NAME', 'rsglider-uploads');
    this.internalEndpoint = endpoint;

    // For external URLs, replace internal Docker hostname with localhost
    this.externalEndpoint = this.configService.get<string>('S3_EXTERNAL_ENDPOINT') ||
      endpoint.replace('minio:9000', 'localhost:9000');

    // Internal S3 client for operations within Docker network
    this.s3Client = new S3Client({
      endpoint,
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
      forcePathStyle,
    });

    // External S3 client for generating URLs accessible from outside Docker
    this.externalS3Client = new S3Client({
      endpoint: this.externalEndpoint,
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
      forcePathStyle,
    });

    this.logger.log(`S3 Service initialized with internal endpoint: ${endpoint}`);
    this.logger.log(`S3 Service external endpoint: ${this.externalEndpoint}`);
  }

  /**
   * Initialize S3 buckets and setup CORS
   */
  async initializeBuckets(): Promise<void> {
    try {
      // Create default bucket if it doesn't exist
      await this.createBucketIfNotExists(this.defaultBucket);

      // Setup CORS for the bucket (optional for MinIO compatibility)
      try {
        await this.setupBucketCors(this.defaultBucket);
        this.logger.log(`CORS configured for bucket: ${this.defaultBucket}`);
      } catch (corsError) {
        this.logger.warn(`CORS setup failed (MinIO compatibility issue): ${corsError.message}`);
        this.logger.log('Continuing without CORS configuration - this is normal for MinIO');
      }

      this.logger.log(`S3 buckets initialized successfully`);
    } catch (error) {
      this.logger.error('Failed to initialize S3 buckets', error);
      throw error;
    }
  }

  /**
   * Upload a file to S3
   */
  async uploadFile(options: UploadOptions): Promise<{ key: string; etag: string; location: string }> {
    const bucket = options.bucket || this.defaultBucket;
    const contentType = options.contentType || mimeTypes.lookup(options.key) || 'application/octet-stream';

    try {
      const command = new PutObjectCommand({
        Bucket: bucket,
        Key: options.key,
        Body: options.body,
        ContentType: contentType,
        Metadata: options.metadata,
        ...(options.expires && { Expires: new Date(Date.now() + options.expires * 1000) }),
      });

      const result = await this.s3Client.send(command);

      const location = `${this.configService.get('S3_ENDPOINT')}/${bucket}/${options.key}`;

      this.logger.log(`File uploaded successfully: ${options.key}`);

      return {
        key: options.key,
        etag: result.ETag,
        location,
      };
    } catch (error) {
      this.logger.error(`Failed to upload file: ${options.key}`, error);
      throw error;
    }
  }

  /**
   * Generate a presigned URL for uploading
   */
  async getPresignedUploadUrl(options: PresignedUrlOptions): Promise<string> {
    const bucket = options.bucket || this.defaultBucket;
    const expires = options.expires || 3600; // 1 hour default

    try {
      const command = new PutObjectCommand({
        Bucket: bucket,
        Key: options.key,
        ContentType: options.contentType,
      });

      const url = await getSignedUrl(this.externalS3Client, command, { expiresIn: expires });

      this.logger.log(`Generated presigned upload URL for: ${options.key}`);
      return url;
    } catch (error) {
      this.logger.error(`Failed to generate presigned upload URL: ${options.key}`, error);
      throw error;
    }
  }

  /**
   * Generate a presigned URL for downloading
   */
  async getPresignedDownloadUrl(options: PresignedUrlOptions): Promise<string> {
    const bucket = options.bucket || this.defaultBucket;
    const expires = options.expires || 3600; // 1 hour default

    try {
      const command = new GetObjectCommand({
        Bucket: bucket,
        Key: options.key,
      });

      const url = await getSignedUrl(this.externalS3Client, command, { expiresIn: expires });

      this.logger.log(`Generated presigned download URL for: ${options.key}`);
      return url;
    } catch (error) {
      this.logger.error(`Failed to generate presigned download URL: ${options.key}`, error);
      throw error;
    }
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(key: string, bucket?: string): Promise<void> {
    const targetBucket = bucket || this.defaultBucket;

    try {
      const command = new DeleteObjectCommand({
        Bucket: targetBucket,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.log(`File deleted successfully: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file: ${key}`, error);
      throw error;
    }
  }

  /**
   * Check if a file exists in S3
   */
  async fileExists(key: string, bucket?: string): Promise<boolean> {
    const targetBucket = bucket || this.defaultBucket;

    try {
      const command = new HeadObjectCommand({
        Bucket: targetBucket,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        return false;
      }
      this.logger.error(`Failed to check file existence: ${key}`, error);
      throw error;
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(key: string, bucket?: string): Promise<any> {
    const targetBucket = bucket || this.defaultBucket;

    try {
      const command = new HeadObjectCommand({
        Bucket: targetBucket,
        Key: key,
      });

      const result = await this.s3Client.send(command);
      return {
        contentType: result.ContentType,
        contentLength: result.ContentLength,
        lastModified: result.LastModified,
        etag: result.ETag,
        metadata: result.Metadata,
      };
    } catch (error) {
      this.logger.error(`Failed to get file metadata: ${key}`, error);
      throw error;
    }
  }

  /**
   * Create bucket if it doesn't exist
   */
  private async createBucketIfNotExists(bucket: string): Promise<void> {
    try {
      const command = new CreateBucketCommand({ Bucket: bucket });
      await this.s3Client.send(command);
      this.logger.log(`Bucket created: ${bucket}`);
    } catch (error) {
      if (error.name === 'BucketAlreadyOwnedByYou' || error.name === 'BucketAlreadyExists') {
        this.logger.log(`Bucket already exists: ${bucket}`);
      } else {
        throw error;
      }
    }
  }

  /**
   * Setup CORS for bucket (MinIO compatible)
   */
  private async setupBucketCors(bucket: string): Promise<void> {
    try {
      // Simplified CORS configuration for MinIO compatibility
      const corsConfiguration = {
        CORSRules: [
          {
            AllowedHeaders: ['*'],
            AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE'],
            AllowedOrigins: ['*'],
            MaxAgeSeconds: 3000,
          },
        ],
      };

      const command = new PutBucketCorsCommand({
        Bucket: bucket,
        CORSConfiguration: corsConfiguration,
      });

      await this.s3Client.send(command);
      this.logger.log(`CORS configured for bucket: ${bucket}`);
    } catch (error) {
      // Check if it's a MinIO "NotImplemented" error
      if (error.Code === 'NotImplemented' || error.name === 'NotImplemented') {
        this.logger.warn(`CORS setup not supported by MinIO - this is normal for local development`);
        return; // Don't throw, just continue
      }

      this.logger.error(`Failed to setup CORS for bucket: ${bucket}`, error);
      throw error;
    }
  }
}
