import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as fs from 'fs';
import { CreateRepositoryOptionsDto } from '../dto/create-repository-options.dto.js';
import { CreateUserOptionsDto } from '../dto/create-user-options.dto.js';
import {
  GiteaRepositoryResponseDto,
  GiteaVersionResponseDto
} from '../dto/gitea-response.dto.js';
import { GiteaUserDto } from '../dto/gitea-user.dto.js';
import { UpdateUserOptionsDto } from '../dto/update-user-options.dto.js';
import { WebhookOptionsDto } from '../dto/webhook-options.dto.js';

export { GiteaRepositoryResponseDto } from '../dto/gitea-response.dto.js';

@Injectable()
export class GiteaService {
  private readonly logger = new Logger(GiteaService.name);
  private readonly client: ReturnType<typeof axios.create>;
  private readonly baseUrl: string;
  private readonly externalUrl: string;
  private readonly adminToken: string;

  constructor(private configService: ConfigService) {
    this.baseUrl = this.configService.get<string>('GITEA_BASE_URL');
    this.externalUrl = this.configService.get<string>('GITEA_EXTERNAL_URL');
    this.adminToken = this.configService.get<string>('GITEA_ADMIN_TOKEN');

    // Try to read token from shared file if environment variable is not set or is default
    if (!this.adminToken || this.adminToken === 'rsglider_gitea_admin_token_change_in_production') {
      try {
        const tokenPath = '/app/shared/gitea_admin_token.txt';
        if (fs.existsSync(tokenPath)) {
          const fileToken = fs.readFileSync(tokenPath, 'utf8').trim();
          if (fileToken) {
            this.adminToken = fileToken;
            this.logger.log('✅ Using Gitea admin token from shared file');
          }
        }
      } catch (error) {
        this.logger.warn('Could not read Gitea token from shared file:', error.message);
      }
    }

    if (!this.baseUrl || !this.adminToken) {
      throw new Error('Gitea configuration missing: GITEA_BASE_URL and GITEA_ADMIN_TOKEN are required');
    }

    this.client = axios.create({
      baseURL: `${this.baseUrl}/api/v1`,
      headers: {
        'Authorization': `token ${this.adminToken}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });

    this.logger.log(`Gitea Service initialized with base URL: ${this.baseUrl}`);
    this.logger.log(`Gitea Service external URL: ${this.externalUrl}`);
  }

  /**
   * Test connection to Gitea
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/version');
      this.logger.log(`Gitea connection successful. Version: ${(response.data as GiteaVersionResponseDto).version}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to connect to Gitea:', error.message);
      return false;
    }
  }

  /**
   * Get Gitea version information
   */
  async getVersion(): Promise<GiteaVersionResponseDto> {
    try {
      const response = await this.client.get('/version');
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get Gitea version:', error.message);
      throw new BadRequestException('Failed to connect to Gitea');
    }
  }

  // User Management Methods
  /**
   * Create a new user in Gitea
   */
  async createUser(options: CreateUserOptionsDto): Promise<GiteaUserDto> {
    try {
      const response = await this.client.post('/admin/users', {
        username: options.username,
        email: options.email,
        password: options.password,
        full_name: options.full_name || options.username,
        login_name: options.login_name || options.username,
        send_notify: options.send_notify || false,
        source_id: options.source_id || 0,
        must_change_password: options.must_change_password || false,
        restricted: options.restricted || false,
        visibility: options.visibility || 'public',
      });

      this.logger.log(`Created Gitea user: ${options.username} (ID: ${(response.data as any).id})`);
      return response.data as GiteaUserDto;
    } catch (error) {
      this.logger.error(`Failed to create Gitea user ${options.username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to create Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get user by username
   */
  async getUserByUsername(username: string): Promise<GiteaUserDto> {
    try {
      const response = await this.client.get(`/users/${username}`);
      return response.data as GiteaUserDto;
    } catch (error) {
      if (error.response?.status === 404) {
        throw new NotFoundException(`Gitea user not found: ${username}`);
      }
      this.logger.error(`Failed to get Gitea user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Update user information
   */
  async updateUser(username: string, options: UpdateUserOptionsDto): Promise<GiteaUserDto> {
    try {
      const response = await this.client.patch(`/admin/users/${username}`, options);
      this.logger.log(`Updated Gitea user: ${username}`);
      return response.data as GiteaUserDto;
    } catch (error) {
      this.logger.error(`Failed to update Gitea user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to update Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete user
   */
  async deleteUser(username: string): Promise<void> {
    try {
      await this.client.delete(`/admin/users/${username}`);
      this.logger.log(`Deleted Gitea user: ${username}`);
    } catch (error) {
      this.logger.error(`Failed to delete Gitea user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to delete Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  // Repository Management Methods
  /**
   * Get user repositories
   */
  async getUserRepositories(username: string, page: number = 1, limit: number = 50): Promise<GiteaRepositoryResponseDto[]> {
    try {
      const response = await this.client.get(`/users/${username}/repos`, {
        params: { page, limit }
      });
      return response.data as GiteaRepositoryResponseDto[];
    } catch (error) {
      this.logger.error(`Failed to get repositories for user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get repositories: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get repository by owner and name
   */
  async getRepository(owner: string, repo: string): Promise<GiteaRepositoryResponseDto> {
    try {
      const response = await this.client.get(`/repos/${owner}/${repo}`);
      return response.data as GiteaRepositoryResponseDto;
    } catch (error) {
      if (error.response?.status === 404) {
        throw new NotFoundException(`Repository not found: ${owner}/${repo}`);
      }
      this.logger.error(`Failed to get repository ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get repository: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Create repository for user
   */
  async createRepository(username: string, options: CreateRepositoryOptionsDto): Promise<GiteaRepositoryResponseDto> {
    try {
      const response = await this.client.post(`/admin/users/${username}/repos`, {
        name: options.name,
        description: options.description || '',
        private: options.private || false,
        auto_init: options.auto_init || true,
        gitignores: options.gitignores || '',
        license: options.license || '',
        readme: options.readme || 'Default',
        default_branch: options.default_branch || 'main',
        trust_model: options.trust_model || 'default',
      });

      this.logger.log(`Created repository: ${username}/${options.name}`);
      return response.data as GiteaRepositoryResponseDto;
    } catch (error) {
      this.logger.error(`Failed to create repository ${username}/${options.name}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to create repository: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete repository
   */
  async deleteRepository(owner: string, repo: string): Promise<void> {
    try {
      await this.client.delete(`/repos/${owner}/${repo}`);
      this.logger.log(`Deleted repository: ${owner}/${repo}`);
    } catch (error) {
      this.logger.error(`Failed to delete repository ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to delete repository: ${error.response?.data?.message || error.message}`);
    }
  }

  // Webhook Management Methods
  /**
   * Create webhook for repository
   */
  async createWebhook(owner: string, repo: string, options: WebhookOptionsDto): Promise<any> {
    try {
      const response = await this.client.post(`/repos/${owner}/${repo}/hooks`, {
        type: options.type,
        config: options.config,
        events: options.events,
        active: options.active !== false,
        branch_filter: options.branch_filter || '',
      });

      this.logger.log(`Created webhook for repository: ${owner}/${repo}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to create webhook for ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to create webhook: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get repository webhooks
   */
  async getWebhooks(owner: string, repo: string): Promise<any[]> {
    try {
      const response = await this.client.get(`/repos/${owner}/${repo}/hooks`);
      return response.data as any[];
    } catch (error) {
      this.logger.error(`Failed to get webhooks for ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get webhooks: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(owner: string, repo: string, hookId: number): Promise<void> {
    try {
      await this.client.delete(`/repos/${owner}/${repo}/hooks/${hookId}`);
      this.logger.log(`Deleted webhook ${hookId} for repository: ${owner}/${repo}`);
    } catch (error) {
      this.logger.error(`Failed to delete webhook ${hookId} for ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to delete webhook: ${error.response?.data?.message || error.message}`);
    }
  }

  // Utility Methods
  /**
   * Get external URL for repository
   */
  getRepositoryExternalUrl(owner: string, repo: string): string {
    return `${this.externalUrl}/${owner}/${repo}`;
  }

  /**
   * Get clone URL for repository
   */
  getRepositoryCloneUrl(owner: string, repo: string, useSSH: boolean = false): string {
    if (useSSH) {
      const domain = this.externalUrl.replace(/^https?:\/\//, '');
      return `git@${domain}:${owner}/${repo}.git`;
    }
    return `${this.externalUrl}/${owner}/${repo}.git`;
  }
}
