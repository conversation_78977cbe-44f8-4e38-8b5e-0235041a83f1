import { DatabaseModule } from '../../database/database.module.js';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ClientReleasesService } from './client-releases.service.js';
import { DatabaseService } from './database.service.js';
import { DeveloperManagementService } from './developer-management.service.js';
import { FileUploadsService } from './file-uploads.service.js';
import { GiteaService } from './gitea.service.js';
import { RedisService } from './redis.service.js';
import { RepositorySyncService } from './repository-sync.service.js';
import { S3Service } from './s3.service.js';
import { WebhookProcessorService } from './webhook-processor.service.js';

@Module({
  imports: [ConfigModule, DatabaseModule],
  providers: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
  exports: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
})
export class ServicesModule {}
