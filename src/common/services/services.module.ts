import { DatabaseModule } from '@/database/database.module';
import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ClientReleasesService } from './client-releases.service';
import { DatabaseService } from './database.service';
import { DeveloperManagementService } from './developer-management.service';
import { FileUploadsService } from './file-uploads.service';
import { GiteaService } from './gitea.service';
import { RedisService } from './redis.service';
import { RepositorySyncService } from './repository-sync.service';
import { S3Service } from './s3.service';
import { WebhookProcessorService } from './webhook-processor.service';

@Module({
  imports: [ConfigModule, DatabaseModule],
  providers: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
  exports: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
})
export class ServicesModule {}
