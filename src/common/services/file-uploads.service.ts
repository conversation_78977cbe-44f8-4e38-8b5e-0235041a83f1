import { FileUpload, fileUploads, NewFileUpload } from '@/database/schema/file-uploads.schema';
import * as schema from '@/database/schema/index';
import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { S3Service } from './s3.service';

export interface UploadFileOptions {
  userId: string;
  file: Express.Multer.File;
  isPublic?: boolean;
  expiresIn?: number; // TTL in seconds
  metadata?: Record<string, any>;
}

export interface PresignedUploadOptions {
  userId: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  isPublic?: boolean;
  expiresIn?: number;
}

const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml'
];

const ALLOWED_VIDEO_TYPES = [
  'video/mp4'
];

const ALLOWED_MIME_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES];

const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB for images
const MAX_VIDEO_SIZE = 100 * 1024 * 1024; // 100MB for videos

@Injectable()
export class FileUploadsService {
  private readonly logger = new Logger(FileUploadsService.name);

  constructor(
    @Inject('DB') private db: PostgresJsDatabase<typeof schema>,
    private s3Service: S3Service,
  ) {}

  /**
   * Upload a file directly
   */
  async uploadFile(options: UploadFileOptions): Promise<FileUpload> {
    const { userId, file, isPublic = false, expiresIn, metadata } = options;

    // Validate file
    this.validateFile(file);

    try {
      // Generate unique filename
      const fileExtension = extname(file.originalname);
      const uniqueFileName = `${uuidv4()}${fileExtension}`;
      const s3Key = this.generateS3Key(userId, uniqueFileName);

      // Determine file type
      const fileType = this.getFileType(file.mimetype);

      // Upload to S3
      const uploadResult = await this.s3Service.uploadFile({
        key: s3Key,
        body: file.buffer,
        contentType: file.mimetype,
        metadata: {
          originalName: file.originalname,
          userId,
          ...metadata,
        },
        expires: expiresIn,
      });

      // Create database record
      const fileUploadData: NewFileUpload = {
        userId,
        originalName: file.originalname,
        fileName: uniqueFileName,
        mimeType: file.mimetype,
        fileType,
        fileSize: file.size.toString(),
        s3Key,
        s3Bucket: this.s3Service['defaultBucket'],
        s3ETag: uploadResult.etag,
        status: 'completed',
        uploadProgress: '100',
        isValidated: true,
        isPublic,
        accessToken: isPublic ? null : this.generateAccessToken(),
        metadata: this.extractFileMetadata(file),
        expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 1000) : null,
      };

      const [fileUpload] = await this.db
        .insert(fileUploads)
        .values(fileUploadData)
        .returning();

      this.logger.log(`File uploaded successfully: ${file.originalname} -> ${s3Key}`);
      return fileUpload;
    } catch (error) {
      this.logger.error('Failed to upload file', error);
      throw error;
    }
  }

  /**
   * Generate presigned upload URL for client-side uploads
   */
  async generatePresignedUploadUrl(options: PresignedUploadOptions): Promise<{
    uploadUrl: string;
    fileId: string;
    s3Key: string;
  }> {
    const { userId, fileName, fileSize, mimeType, isPublic = false, expiresIn } = options;

    // Validate file parameters
    this.validateFileParameters(fileName, fileSize, mimeType);

    try {
      // Generate unique filename and S3 key
      const fileExtension = extname(fileName);
      const uniqueFileName = `${uuidv4()}${fileExtension}`;
      const s3Key = this.generateS3Key(userId, uniqueFileName);

      // Generate presigned URL
      const uploadUrl = await this.s3Service.getPresignedUploadUrl({
        key: s3Key,
        contentType: mimeType,
        expires: 3600, // 1 hour for upload
      });

      // Create pending database record
      const fileUploadData: NewFileUpload = {
        userId,
        originalName: fileName,
        fileName: uniqueFileName,
        mimeType,
        fileType: this.getFileType(mimeType),
        fileSize: fileSize.toString(),
        s3Key,
        s3Bucket: this.s3Service['defaultBucket'],
        status: 'pending',
        uploadProgress: '0',
        isValidated: false,
        isPublic,
        accessToken: isPublic ? null : this.generateAccessToken(),
        expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 1000) : null,
      };

      const [fileUpload] = await this.db
        .insert(fileUploads)
        .values(fileUploadData)
        .returning();

      this.logger.log(`Generated presigned upload URL for: ${fileName}`);

      return {
        uploadUrl,
        fileId: fileUpload.id,
        s3Key,
      };
    } catch (error) {
      this.logger.error('Failed to generate presigned upload URL', error);
      throw error;
    }
  }

  /**
   * Mark upload as completed and validate
   */
  async completeUpload(fileId: string, userId: string): Promise<FileUpload> {
    try {
      const fileUpload = await this.getFileUpload(fileId, userId);

      if (fileUpload.status !== 'pending') {
        throw new BadRequestException('Upload is not in pending state');
      }

      // Verify file exists in S3
      const exists = await this.s3Service.fileExists(fileUpload.s3Key, fileUpload.s3Bucket);
      if (!exists) {
        throw new BadRequestException('File not found in storage');
      }

      // Get file metadata from S3
      const s3Metadata = await this.s3Service.getFileMetadata(fileUpload.s3Key, fileUpload.s3Bucket);

      // Update database record
      const [updatedUpload] = await this.db
        .update(fileUploads)
        .set({
          status: 'completed',
          uploadProgress: '100',
          isValidated: true,
          s3ETag: s3Metadata.etag,
          fileSize: s3Metadata.contentLength?.toString(),
          updatedAt: new Date(),
        })
        .where(eq(fileUploads.id, fileId))
        .returning();

      this.logger.log(`Upload completed: ${fileId}`);
      return updatedUpload;
    } catch (error) {
      this.logger.error('Failed to complete upload', error);
      throw error;
    }
  }

  /**
   * Get download URL for a file
   */
  async getDownloadUrl(fileId: string, userId?: string): Promise<string> {
    try {
      const fileUpload = await this.db
        .select()
        .from(schema.fileUploads)
        .where(eq(fileUploads.id, fileId))
        .limit(1);

      if (!fileUpload.length) {
        throw new NotFoundException('File not found');
      }

      const file = fileUpload[0];

      // Check access permissions
      if (!file.isPublic && file.userId !== userId) {
        throw new BadRequestException('Access denied');
      }

      // Check if file is expired
      if (file.expiresAt && file.expiresAt < new Date()) {
        throw new BadRequestException('File has expired');
      }

      // Generate signed download URL
      const downloadUrl = await this.s3Service.getPresignedDownloadUrl({
        key: file.s3Key,
        bucket: file.s3Bucket,
        expires: 3600, // 1 hour
      });

      return downloadUrl;
    } catch (error) {
      this.logger.error('Failed to get download URL', error);
      throw error;
    }
  }

  /**
   * Delete a file
   */
  async deleteFile(fileId: string, userId: string): Promise<void> {
    try {
      const fileUpload = await this.getFileUpload(fileId, userId);

      // Delete from S3
      await this.s3Service.deleteFile(fileUpload.s3Key, fileUpload.s3Bucket);

      // Mark as deleted in database
      await this.db
        .update(fileUploads)
        .set({
          status: 'deleted',
          deletedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(fileUploads.id, fileId));

      this.logger.log(`File deleted: ${fileId}`);
    } catch (error) {
      this.logger.error('Failed to delete file', error);
      throw error;
    }
  }

  /**
   * Get user's files
   */
  async getUserFiles(userId: string): Promise<FileUpload[]> {
    try {
      const files = await this.db
        .select()
        .from(fileUploads)
        .where(
          and(
            eq(fileUploads.userId, userId),
            eq(fileUploads.status, 'completed')
          )
        );

      return files;
    } catch (error) {
      this.logger.error('Failed to get user files', error);
      throw error;
    }
  }

  private async getFileUpload(fileId: string, userId: string): Promise<FileUpload> {
    const fileUpload = await this.db
      .select()
      .from(fileUploads)
      .where(
        and(
          eq(fileUploads.id, fileId),
          eq(fileUploads.userId, userId)
        )
      )
      .limit(1);

    if (!fileUpload.length) {
      throw new NotFoundException('File not found');
    }

    return fileUpload[0];
  }

  private validateFile(file: Express.Multer.File): void {
    if (!ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type not allowed. Allowed types: ${ALLOWED_MIME_TYPES.join(', ')}`
      );
    }

    if (file.size > MAX_FILE_SIZE) {
      throw new BadRequestException(`File too large. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB`);
    }

    if (ALLOWED_IMAGE_TYPES.includes(file.mimetype) && file.size > MAX_IMAGE_SIZE) {
      throw new BadRequestException(`Image too large. Maximum size: ${MAX_IMAGE_SIZE / 1024 / 1024}MB`);
    }

    if (ALLOWED_VIDEO_TYPES.includes(file.mimetype) && file.size > MAX_VIDEO_SIZE) {
      throw new BadRequestException(`Video too large. Maximum size: ${MAX_VIDEO_SIZE / 1024 / 1024}MB`);
    }
  }

  private validateFileParameters(_fileName: string, fileSize: number, mimeType: string): void {
    if (!ALLOWED_MIME_TYPES.includes(mimeType)) {
      throw new BadRequestException(
        `File type not allowed. Allowed types: ${ALLOWED_MIME_TYPES.join(', ')}`
      );
    }

    if (fileSize > MAX_FILE_SIZE) {
      throw new BadRequestException(`File too large. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB`);
    }
  }

  private getFileType(mimeType: string): 'image' | 'video' | 'document' | 'archive' | 'executable' {
    if (ALLOWED_IMAGE_TYPES.includes(mimeType)) return 'image';
    if (ALLOWED_VIDEO_TYPES.includes(mimeType)) return 'video';
    return 'document';
  }

  private generateS3Key(userId: string, fileName: string): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `uploads/${userId}/${year}/${month}/${day}/${fileName}`;
  }

  private generateAccessToken(): string {
    return uuidv4().replace(/-/g, '');
  }

  private extractFileMetadata(file: Express.Multer.File): any {
    const metadata: any = {
      originalName: file.originalname,
      encoding: file.encoding,
    };

    // For images, we could extract dimensions here using a library like sharp
    // For videos, we could extract duration, resolution, etc. using ffprobe
    // For now, we'll just return basic metadata

    return metadata;
  }
}
