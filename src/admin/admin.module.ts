import { DatabaseModule } from '@/database/database.module';
import { UsersModule } from '@/users/users.module';
import { Module } from '@nestjs/common';
import { AdminController } from './.*.js.js'admin.controller';
import { PermissionsService } from './.*.js.js'permissions.service';
import { RolesService } from './.*.js.js'roles.service';

@Module({
  imports: [UsersModule, DatabaseModule],
  controllers: [AdminController],
  providers: [RolesService, PermissionsService],
})
export class AdminModule {} 