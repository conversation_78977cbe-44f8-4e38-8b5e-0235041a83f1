import { DatabaseModule } from '@/database/database.module';
import { UsersModule } from '@/users/users.module';
import { Module } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { PermissionsService } from './permissions.service';
import { RolesService } from './roles.service';

@Module({
  imports: [UsersModule, DatabaseModule],
  controllers: [AdminController],
  providers: [RolesService, PermissionsService],
})
export class AdminModule {} 