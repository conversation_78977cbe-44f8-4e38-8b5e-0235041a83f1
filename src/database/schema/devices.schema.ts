import { relations } from 'drizzle-orm';
import { boolean, index, json, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { users } from './users.schema.js.js';

export const devices = pgTable('devices', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  deviceName: varchar('device_name', { length: 255 }).notNull(),
  deviceType: varchar('device_type', { length: 50 }).notNull(), // e.g., 'desktop', 'mobile', 'web'
  platform: varchar('platform', { length: 50 }), // e.g., 'macOS', 'Windows', 'Linux', 'iOS', 'Android'
  deviceInfo: json('device_info').$type<{
    userAgent?: string;
    browser?: string;
    os?: string;
    model?: string;
    trusted?: boolean;
  }>(),
  isTrusted: boolean('is_trusted').notNull().default(false),
  registeredAt: timestamp('registered_at').notNull().defaultNow(),
  lastActivityAt: timestamp('last_activity_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}, (table) => ({
  userIdIdx: index('devices_user_id_idx').on(table.userId),
  deviceNameIdx: index('devices_device_name_idx').on(table.deviceName),
}));

export const devicesRelations = relations(devices, ({ one }) => ({
  user: one(users, {
    fields: [devices.userId],
    references: [users.id],
  }),
}));

export type Device = typeof devices.$inferSelect;
export type NewDevice = typeof devices.$inferInsert;