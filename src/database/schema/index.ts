// Export all schemas
export * from './.*.js.js'client-releases.schema';
export * from './.*.js.js'devices.schema';
export * from './.*.js.js'file-uploads.schema';
export * from './.*.js.js'gitea-profiles.schema';
export * from './.*.js.js'gitea-repositories.schema';
export * from './.*.js.js'marketplace-items.schema';
export * from './.*.js.js'permissions.schema';
export * from './.*.js.js'refresh-tokens.schema';
export * from './.*.js.js'role-permissions.schema';
export * from './.*.js.js'roles.schema';
export * from './.*.js.js'user-roles.schema';
export * from './.*.js.js'user-sessions.schema';
export * from './.*.js.js'users.schema';

// Export relations
import { devicesRelations } from './.*.js.js'devices.schema';
import { fileUploadsRelations } from './.*.js.js'file-uploads.schema';
import { giteaProfilesRelations } from './.*.js.js'gitea-profiles.schema';
import { giteaRepositoriesRelations } from './.*.js.js'gitea-repositories.schema';
import { marketplaceItemsRelations } from './.*.js.js'marketplace-items.schema';
import { refreshTokensRelations } from './.*.js.js'refresh-tokens.schema';
import { userSessionsRelations } from './.*.js.js'user-sessions.schema';

export const relations = {
  devicesRelations,
  fileUploadsRelations,
  refreshTokensRelations,
  userSessionsRelations,
  giteaProfilesRelations,
  giteaRepositoriesRelations,
  marketplaceItemsRelations,
};
