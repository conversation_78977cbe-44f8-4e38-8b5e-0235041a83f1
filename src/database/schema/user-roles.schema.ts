import { pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { roles } from './.*.js.js'roles.schema';
import { users } from './.*.js.js'users.schema';

export const userRoles = pgTable('user_roles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id),
  roleId: uuid('role_id').notNull().references(() => roles.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}); 