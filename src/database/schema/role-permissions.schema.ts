import { pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { permissions } from './.*.js.js'permissions.schema';
import { roles } from './.*.js.js'roles.schema';

export const rolePermissions = pgTable('role_permissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  roleId: uuid('role_id').notNull().references(() => roles.id),
  permissionId: uuid('permission_id').notNull().references(() => permissions.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}); 