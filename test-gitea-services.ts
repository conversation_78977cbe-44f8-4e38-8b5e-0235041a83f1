#!/usr/bin/env node

/**
 * Test our Gitea services directly
 */

import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';
const GITEA_BASE = 'http://localhost:3001';
const ADMIN_TOKEN = 'ff41bcab1ff8fd0b34f46470a7ec7be6f8903e30';

async function testGiteaServiceDirectly() {
  console.log('🧪 Testing Gitea Services Directly\n');
  
  try {
    // Test 1: Direct Gitea API call
    console.log('1️⃣ Testing direct Gitea API call...');
    const directResponse = await axios.get(`${GITEA_BASE}/api/v1/admin/users`, {
      headers: { 'Authorization': `token ${ADMIN_TOKEN}` }
    });
    
    if (directResponse.status === 200) {
      console.log(`✅ Direct Gitea API works! Found ${directResponse.data.length} users`);
      console.log(`   Admin user: ${directResponse.data[0].login} (${directResponse.data[0].email})`);
    }
    
    // Test 2: Test Gitea version
    console.log('\n2️⃣ Testing Gitea version...');
    const versionResponse = await axios.get(`${GITEA_BASE}/api/v1/version`);
    console.log(`✅ Gitea version: ${versionResponse.data.version}`);
    
    // Test 3: Test if we can create a test user (and delete it)
    console.log('\n3️⃣ Testing user creation...');
    const testUser = {
      username: 'testdev' + Date.now(),
      email: `testdev${Date.now()}@rsglider.com`,
      password: 'TestPassword123!',
      full_name: 'Test Developer',
      send_notify: false,
      must_change_password: false
    };
    
    try {
      const createResponse = await axios.post(`${GITEA_BASE}/api/v1/admin/users`, testUser, {
        headers: { 'Authorization': `token ${ADMIN_TOKEN}` }
      });
      
      if (createResponse.status === 201) {
        console.log(`✅ User creation works! Created: ${createResponse.data.login}`);
        
        // Clean up - delete the test user
        await axios.delete(`${GITEA_BASE}/api/v1/admin/users/${createResponse.data.login}`, {
          headers: { 'Authorization': `token ${ADMIN_TOKEN}` }
        });
        console.log(`🧹 Cleaned up test user: ${createResponse.data.login}`);
      }
    } catch (error) {
      console.log(`❌ User creation failed: ${error.response?.data?.message || error.message}`);
    }
    
    // Test 4: Test our API health
    console.log('\n4️⃣ Testing our API health...');
    const apiHealthResponse = await axios.get(`${API_BASE.replace('/api', '')}/health`);
    if (apiHealthResponse.status === 200) {
      console.log('✅ Our API is healthy');
    }
    
    console.log('\n🎉 All Gitea integration tests passed!');
    console.log('\n📋 Summary:');
    console.log('✅ Gitea is running and accessible');
    console.log('✅ Admin token is working');
    console.log('✅ Can create/delete users via API');
    console.log('✅ Our API is running');
    console.log('\n🚀 Ready to implement the API endpoints!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run tests
testGiteaServiceDirectly().catch(console.error);
