{"compilerOptions": {"module": "NodeNext", "moduleResolution": "NodeNext", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "paths": {"@/*": ["./src/*"], "@/admin/*": ["./src/admin/*"], "@/auth/*": ["./src/auth/*"], "@/client/*": ["./src/client/*"], "@/common/*": ["./src/common/*"], "@/config/*": ["./src/config/*"], "@/database/*": ["./src/database/*"], "@/developer/*": ["./src/developer/*"], "@/dto/*": ["./src/users/dto/*", "./src/auth/dto/*", "./src/developer/dto/*", "./src/webhooks/dto/*"], "@/services/*": ["./src/common/services/*"], "@/strategies/*": ["./src/auth/strategies/*"], "@/schema/*": ["./src/database/schema/*"], "@/uploads/*": ["./src/uploads/*"], "@/users/*": ["./src/users/*"], "@/webhooks/*": ["./src/webhooks/*"]}}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"]}