{"compilerOptions": {"module": "CommonJS", "moduleResolution": "node", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "rootDir": "./src", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "allowImportingTsExtensions": false, "resolveJsonModule": true, "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"]}