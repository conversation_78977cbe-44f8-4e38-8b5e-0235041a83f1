#!/bin/bash

# Gitea Admin User Auto-Creation Script
# This script automatically creates an admin user and generates an API token

set -e

echo "🔧 Gitea Admin Initialization Script"

# Configuration
ADMIN_USERNAME="admin"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="rsglider_admin_password_change_in_production"
TOKEN_NAME="rsglider_api_token"
EXPECTED_TOKEN="rsglider_gitea_admin_token_change_in_production"

# Wait for Gite<PERSON> to be ready
echo "⏳ Waiting for Gite<PERSON> to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if curl -f http://gitea:3000/api/healthz >/dev/null 2>&1; then
        echo "✅ Gitea is ready!"
        break
    fi
    echo "   Gitea not ready yet, waiting... (attempt $((attempt + 1))/$max_attempts)"
    sleep 5
    attempt=$((attempt + 1))
done

if [ $attempt -eq $max_attempts ]; then
    echo "❌ Gitea failed to become ready after $max_attempts attempts"
    exit 1
fi

# Wait a bit more for <PERSON><PERSON><PERSON> to fully initialize
sleep 10

# Create admin user using Gitea CLI
echo "👤 Creating admin user..."
cd /data/gitea

# Check if user already exists by trying to create it
if ./gitea admin user create \
    --username "$ADMIN_USERNAME" \
    --email "$ADMIN_EMAIL" \
    --password "$ADMIN_PASSWORD" \
    --admin \
    --must-change-password=false 2>/dev/null; then
    echo "✅ Admin user '$ADMIN_USERNAME' created successfully"
else
    echo "ℹ️  Admin user '$ADMIN_USERNAME' may already exist"
fi

# Generate API token
echo "🔑 Generating API token..."
if TOKEN_RESULT=$(./gitea admin user generate-access-token \
    --username "$ADMIN_USERNAME" \
    --token-name "$TOKEN_NAME" \
    --scopes "all" 2>/dev/null); then
    echo "✅ API token generated: $TOKEN_RESULT"

    # Save token to a file for the API to read
    echo "$TOKEN_RESULT" > /shared/gitea_admin_token.txt
    echo "💾 Token saved to /shared/gitea_admin_token.txt"
else
    echo "ℹ️  API token generation may have failed or token already exists"
    # Try to use the expected token
    echo "$EXPECTED_TOKEN" > /shared/gitea_admin_token.txt
    echo "💾 Using expected token: $EXPECTED_TOKEN"
fi

echo "🎉 Gitea admin initialization completed!"
echo ""
echo "📋 Admin Credentials:"
echo "   Username: $ADMIN_USERNAME"
echo "   Email:    $ADMIN_EMAIL"
echo "   Password: $ADMIN_PASSWORD"
echo "   Web UI:   http://localhost:3001"
echo ""
echo "✅ Gitea is ready for RSGlider integration!"
